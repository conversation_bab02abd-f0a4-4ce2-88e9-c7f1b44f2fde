/**
 * AI内容推荐面板组件
 * 提供智能内容推荐功能
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Input,
  Select,
  Button,
  List,
  Space,
  Typography,
  Tag,
  Rate,
  Image,
  Tabs,
  Badge,
  Tooltip,
  Avatar,
  Progress,
  Empty,
  Spin,
  Row,
  Col,
  Divider,
  Dropdown
} from 'antd';
import {
  HeartOutlined,
  HeartFilled,
  DownloadOutlined,
  EyeOutlined,
  StarOutlined,
  BulbOutlined,
  AppstoreOutlined,
  PictureOutlined,
  BgColorsOutlined,
  LayoutOutlined,
  FontSizeOutlined,
  ReloadOutlined,
  MoreOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import AIContentRecommendationService, {
  RecommendationItem,
  RecommendationResult,
  RecommendationType,
  RecommendationContext
} from '../../services/AIContentRecommendationService';
import './AIContentRecommendationPanel.less';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

interface AIContentRecommendationPanelProps {
  visible: boolean;
  onClose: () => void;
  context?: RecommendationContext;
}

const AIContentRecommendationPanel: React.FC<AIContentRecommendationPanelProps> = ({
  visible,
  onClose,
  context
}) => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<RecommendationType | 'all'>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [recommendations, setRecommendations] = useState<RecommendationResult | null>(null);
  const [filteredItems, setFilteredItems] = useState<RecommendationItem[]>([]);
  const [bookmarks, setBookmarks] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  const recommendationService = AIContentRecommendationService.getInstance();

  useEffect(() => {
    if (visible) {
      loadData();
      setupEventListeners();
    }

    return () => {
      cleanupEventListeners();
    };
  }, [visible]);

  useEffect(() => {
    if (recommendations) {
      filterRecommendations();
    }
  }, [recommendations, selectedType, selectedCategory, searchQuery]);

  const setupEventListeners = () => {
    recommendationService.on('recommendationCompleted', handleRecommendationCompleted);
    recommendationService.on('bookmarkAdded', handleBookmarkAdded);
    recommendationService.on('bookmarkRemoved', handleBookmarkRemoved);
  };

  const cleanupEventListeners = () => {
    recommendationService.off('recommendationCompleted', handleRecommendationCompleted);
    recommendationService.off('bookmarkAdded', handleBookmarkAdded);
    recommendationService.off('bookmarkRemoved', handleBookmarkRemoved);
  };

  const loadData = () => {
    const preferences = recommendationService.getUserPreferences();
    setBookmarks(preferences.bookmarks);
    
    // 如果有上下文，自动获取推荐
    if (context) {
      handleSearch('');
    }
  };

  const handleRecommendationCompleted = (result: RecommendationResult) => {
    setRecommendations(result);
    setIsLoading(false);
  };

  const handleBookmarkAdded = (itemId: string) => {
    setBookmarks(prev => [...prev, itemId]);
  };

  const handleBookmarkRemoved = (itemId: string) => {
    setBookmarks(prev => prev.filter(id => id !== itemId));
  };

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    setIsLoading(true);

    try {
      await recommendationService.getRecommendations(query || 'general', context);
    } catch (error) {
      console.error('Failed to get recommendations:', error);
      setIsLoading(false);
    }
  };

  const filterRecommendations = () => {
    if (!recommendations) {
      setFilteredItems([]);
      return;
    }

    let items = recommendations.items;

    // 按类型过滤
    if (selectedType !== 'all') {
      items = items.filter(item => item.type === selectedType);
    }

    // 按分类过滤
    if (selectedCategory !== 'all') {
      items = items.filter(item => item.category === selectedCategory);
    }

    // 按搜索词过滤
    if (searchQuery) {
      items = items.filter(item =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    setFilteredItems(items);
  };

  const handleToggleBookmark = (item: RecommendationItem) => {
    if (bookmarks.includes(item.id)) {
      recommendationService.removeFromBookmarks(item.id);
    } else {
      recommendationService.addToBookmarks(item.id);
    }
  };

  const handleUseItem = (item: RecommendationItem) => {
    recommendationService.recordUsage(item.id);
    // 这里可以添加使用项目的逻辑
    console.log('Using item:', item);
  };

  const handleDownloadItem = (item: RecommendationItem) => {
    // 模拟下载
    console.log('Downloading item:', item);
  };

  // 获取类型图标
  const getTypeIcon = (type: RecommendationType) => {
    switch (type) {
      case RecommendationType.COMPONENT:
        return <AppstoreOutlined />;
      case RecommendationType.TEMPLATE:
        return <LayoutOutlined />;
      case RecommendationType.ICON:
        return <StarOutlined />;
      case RecommendationType.IMAGE:
        return <PictureOutlined />;
      case RecommendationType.COLOR_PALETTE:
        return <BgColorsOutlined />;
      case RecommendationType.FONT:
        return <FontSizeOutlined />;
      default:
        return <BulbOutlined />;
    }
  };

  // 获取置信度颜色
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return '#52c41a';
    if (confidence >= 70) return '#faad14';
    return '#ff4d4f';
  };

  // 渲染推荐项目
  const renderRecommendationItem = (item: RecommendationItem) => {
    const isBookmarked = bookmarks.includes(item.id);

    const actions = [
      <Tooltip title={isBookmarked ? t('ai.recommendation.removeBookmark') : t('ai.recommendation.addBookmark')}>
        <Button
          type="text"
          icon={isBookmarked ? <HeartFilled style={{ color: '#ff4d4f' }} /> : <HeartOutlined />}
          onClick={() => handleToggleBookmark(item)}
        />
      </Tooltip>,
      <Tooltip title={t('ai.recommendation.preview')}>
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => handleUseItem(item)}
        />
      </Tooltip>,
      <Tooltip title={t('ai.recommendation.download')}>
        <Button
          type="text"
          icon={<DownloadOutlined />}
          onClick={() => handleDownloadItem(item)}
        />
      </Tooltip>,
      <Dropdown
        menu={{
          items: [
            {
              key: 'use',
              label: t('ai.recommendation.use'),
              onClick: () => handleUseItem(item)
            },
            {
              key: 'copy',
              label: t('ai.recommendation.copyLink')
            },
            {
              key: 'report',
              label: t('ai.recommendation.report')
            }
          ]
        }}
      >
        <Button type="text" icon={<MoreOutlined />} />
      </Dropdown>
    ];

    return (
      <List.Item actions={actions}>
        <List.Item.Meta
          avatar={
            <Avatar
              size={64}
              src={item.thumbnail}
              icon={getTypeIcon(item.type)}
              style={{ backgroundColor: '#f0f2f5' }}
            />
          }
          title={
            <Space>
              <Text strong>{item.title}</Text>
              <Tag color="blue">{item.type}</Tag>
              <Progress
                percent={item.confidence}
                size="small"
                strokeColor={getConfidenceColor(item.confidence)}
                showInfo={false}
                style={{ width: 60 }}
              />
            </Space>
          }
          description={
            <div>
              <Paragraph ellipsis={{ rows: 2 }}>{item.description}</Paragraph>
              <Space wrap style={{ marginTop: 8 }}>
                {item.tags.slice(0, 3).map(tag => (
                  <Tag key={tag}>{tag}</Tag>
                ))}
                {item.tags.length > 3 && (
                  <Tag>+{item.tags.length - 3}</Tag>
                )}
              </Space>
              <div style={{ marginTop: 8 }}>
                <Space>
                  <Text type="secondary">
                    {t('ai.recommendation.confidence')}: {item.confidence}%
                  </Text>
                  <Text type="secondary">
                    {t('ai.recommendation.popularity')}: {item.popularity}%
                  </Text>
                  {item.metadata.rating && (
                    <Rate disabled value={item.metadata.rating} style={{ fontSize: '12px' }} />
                  )}
                </Space>
              </div>
            </div>
          }
        />
      </List.Item>
    );
  };

  // 渲染过滤器
  const renderFilters = () => (
    <Row gutter={[8, 8]} style={{ marginBottom: 16 }}>
      <Col flex="auto">
        <Search
          placeholder={t('ai.recommendation.searchPlaceholder') || '搜索推荐内容...'}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onSearch={handleSearch}
          enterButton
        />
      </Col>
      <Col>
        <Select
          value={selectedType}
          onChange={setSelectedType}
          style={{ width: 120 }}
          placeholder={t('ai.recommendation.type') || '类型'}
        >
          <Option value="all">{t('ai.recommendation.allTypes')}</Option>
          <Option value={RecommendationType.COMPONENT}>{t('ai.recommendation.components')}</Option>
          <Option value={RecommendationType.TEMPLATE}>{t('ai.recommendation.templates')}</Option>
          <Option value={RecommendationType.ICON}>{t('ai.recommendation.icons')}</Option>
          <Option value={RecommendationType.IMAGE}>{t('ai.recommendation.images')}</Option>
          <Option value={RecommendationType.COLOR_PALETTE}>{t('ai.recommendation.colors')}</Option>
        </Select>
      </Col>
      <Col>
        <Select
          value={selectedCategory}
          onChange={setSelectedCategory}
          style={{ width: 120 }}
          placeholder={t('ai.recommendation.category') || '分类'}
        >
          <Option value="all">{t('ai.recommendation.allCategories')}</Option>
          {recommendations?.categories.map(cat => (
            <Option key={cat.category} value={cat.category}>
              {cat.category} ({cat.count})
            </Option>
          ))}
        </Select>
      </Col>
      <Col>
        <Button
          icon={<ReloadOutlined />}
          onClick={() => handleSearch(searchQuery)}
          loading={isLoading}
        >
          {t('ai.recommendation.refresh')}
        </Button>
      </Col>
    </Row>
  );

  // 渲染分类标签页
  const renderCategoryTabs = () => {
    if (!recommendations) return null;

    return (
      <Tabs activeKey={activeTab} onChange={setActiveTab} style={{ marginTop: 16 }}>
        <TabPane
          tab={
            <Badge count={filteredItems.length}>
              {t('ai.recommendation.all')}
            </Badge>
          }
          key="all"
        >
          <List
            dataSource={filteredItems}
            renderItem={renderRecommendationItem}
            locale={{ emptyText: <Empty description={t('ai.recommendation.noResults')} /> }}
            pagination={{ pageSize: 10, showSizeChanger: false }}
          />
        </TabPane>

        {recommendations.categories.map(category => {
          const categoryItems = filteredItems.filter(item => item.category === category.category);
          return (
            <TabPane
              tab={
                <Badge count={categoryItems.length}>
                  {category.category}
                </Badge>
              }
              key={category.category}
            >
              <List
                dataSource={categoryItems}
                renderItem={renderRecommendationItem}
                locale={{ emptyText: <Empty description={t('ai.recommendation.noResults')} /> }}
                pagination={{ pageSize: 10, showSizeChanger: false }}
              />
            </TabPane>
          );
        })}

        <TabPane
          tab={
            <Badge count={bookmarks.length}>
              <HeartOutlined />
              {t('ai.recommendation.bookmarks')}
            </Badge>
          }
          key="bookmarks"
        >
          <List
            dataSource={filteredItems.filter(item => bookmarks.includes(item.id))}
            renderItem={renderRecommendationItem}
            locale={{ emptyText: <Empty description={t('ai.recommendation.noBookmarks')} /> }}
            pagination={{ pageSize: 10, showSizeChanger: false }}
          />
        </TabPane>
      </Tabs>
    );
  };

  return (
    <div className="ai-content-recommendation-panel">
      <Card
        title={
          <Space>
            <BulbOutlined />
            {t('ai.recommendation.contentRecommendations')}
          </Space>
        }
        extra={
          <Button type="text" onClick={onClose}>
            {t('common.close')}
          </Button>
        }
      >
        {renderFilters()}

        <Spin spinning={isLoading}>
          {recommendations ? (
            <>
              {recommendations.suggestions.length > 0 && (
                <Card size="small" style={{ marginBottom: 16 }}>
                  <Title level={5}>{t('ai.recommendation.suggestions')}</Title>
                  <Space wrap>
                    {recommendations.suggestions.map((suggestion, index) => (
                      <Tag
                        key={index}
                        color="blue"
                        style={{ cursor: 'pointer' }}
                        onClick={() => handleSearch(suggestion)}
                      >
                        {suggestion}
                      </Tag>
                    ))}
                  </Space>
                </Card>
              )}

              {renderCategoryTabs()}
            </>
          ) : (
            <Empty
              description={t('ai.recommendation.startSearching')}
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default AIContentRecommendationPanel;
