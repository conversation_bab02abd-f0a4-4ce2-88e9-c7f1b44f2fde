/**
 * 照片上传面板
 * 提供照片上传、预处理和质量检测功能
 */
import React, { useState, useCallback, useRef } from 'react';
import {
  Card,
  Upload,
  Button,
  Progress,
  Alert,
  Space,
  Typography,
  Image,
  Row,
  Col,
  Tooltip,
  message
} from 'antd';
import {
  UploadOutlined,
  CameraOutlined,
  DeleteOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';

const { Text, Title } = Typography;

/**
 * 照片质量评估结果接口
 */
export interface PhotoQualityAssessment {
  score: number;
  issues: string[];
  suggestions: string[];
  faceDetected: boolean;
  resolution: {
    width: number;
    height: number;
  };
  lighting: 'good' | 'poor' | 'acceptable';
  blur: 'none' | 'slight' | 'severe';
}

/**
 * 组件属性接口
 */
interface PhotoUploadPanelProps {
  /** 是否禁用 */
  disabled?: boolean;
  /** 上传成功回调 */
  onUploadSuccess?: (file: File, assessment: PhotoQualityAssessment) => void;
  /** 上传失败回调 */
  onUploadError?: (error: Error) => void;
  /** 照片移除回调 */
  onPhotoRemove?: () => void;
}

/**
 * 照片上传面板组件
 */
export const PhotoUploadPanel: React.FC<PhotoUploadPanelProps> = ({
  disabled = false,
  onUploadSuccess,
  onUploadError,
  onPhotoRemove
}) => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [qualityAssessment, setQualityAssessment] = useState<PhotoQualityAssessment | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  /**
   * 分析照片质量
   */
  const analyzePhotoQuality = useCallback(async (file: File): Promise<PhotoQualityAssessment> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        // 模拟质量分析
        const width = img.width;
        const height = img.height;
        const minDimension = Math.min(width, height);
        
        let score = 0.5;
        const issues: string[] = [];
        const suggestions: string[] = [];

        // 分辨率检查
        if (minDimension < 400) {
          issues.push('分辨率过低');
          suggestions.push('建议使用至少400x400像素的照片');
          score -= 0.2;
        } else if (minDimension >= 800) {
          score += 0.2;
        }

        // 宽高比检查
        const aspectRatio = width / height;
        if (aspectRatio < 0.8 || aspectRatio > 1.2) {
          issues.push('宽高比不理想');
          suggestions.push('建议使用接近正方形的照片');
          score -= 0.1;
        }

        // 文件大小检查
        if (file.size > 10 * 1024 * 1024) {
          issues.push('文件过大');
          suggestions.push('建议压缩照片到10MB以下');
          score -= 0.1;
        }

        // 模拟面部检测
        const faceDetected = Math.random() > 0.1; // 90%概率检测到面部
        if (!faceDetected) {
          issues.push('未检测到面部');
          suggestions.push('请确保照片中包含清晰的正面人脸');
          score -= 0.3;
        } else {
          score += 0.2;
        }

        // 模拟光照评估
        const lightingScore = Math.random();
        let lighting: 'good' | 'poor' | 'acceptable';
        if (lightingScore > 0.7) {
          lighting = 'good';
          score += 0.1;
        } else if (lightingScore > 0.4) {
          lighting = 'acceptable';
        } else {
          lighting = 'poor';
          issues.push('光照条件不佳');
          suggestions.push('建议在光线充足的环境下拍摄');
          score -= 0.2;
        }

        // 模拟模糊检测
        const blurScore = Math.random();
        let blur: 'none' | 'slight' | 'severe';
        if (blurScore > 0.8) {
          blur = 'none';
          score += 0.1;
        } else if (blurScore > 0.5) {
          blur = 'slight';
        } else {
          blur = 'severe';
          issues.push('照片模糊');
          suggestions.push('请使用清晰的照片');
          score -= 0.2;
        }

        // 确保分数在0-1范围内
        score = Math.max(0, Math.min(1, score));

        resolve({
          score,
          issues,
          suggestions,
          faceDetected,
          resolution: { width, height },
          lighting,
          blur
        });
      };

      img.src = URL.createObjectURL(file);
    });
  }, []);

  /**
   * 处理文件上传
   */
  const handleUpload: UploadProps['customRequest'] = useCallback(async (options) => {
    const { file, onSuccess, onError } = options;
    const uploadFile = file as File;

    setIsProcessing(true);
    setProcessingProgress(0);

    try {
      // 验证文件类型
      if (!uploadFile.type.startsWith('image/')) {
        throw new Error('请上传图片文件');
      }

      // 验证文件大小
      if (uploadFile.size > 20 * 1024 * 1024) {
        throw new Error('文件大小不能超过20MB');
      }

      setProcessingProgress(20);

      // 创建预览URL
      const url = URL.createObjectURL(uploadFile);
      setPreviewUrl(url);
      setProcessingProgress(40);

      // 分析照片质量
      const assessment = await analyzePhotoQuality(uploadFile);
      setQualityAssessment(assessment);
      setProcessingProgress(80);

      // 模拟处理延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      setProcessingProgress(100);

      setUploadedFile(uploadFile);
      onSuccess?.('ok');
      onUploadSuccess?.(uploadFile, assessment);

      if (assessment.score < 0.5) {
        message.warning('照片质量较低，可能影响重建效果');
      } else {
        message.success('照片上传成功');
      }
    } catch (error) {
      console.error('照片上传失败:', error);
      onError?.(error as Error);
      onUploadError?.(error as Error);
      message.error((error as Error).message);
    } finally {
      setIsProcessing(false);
      setTimeout(() => setProcessingProgress(0), 1000);
    }
  }, [analyzePhotoQuality, onUploadSuccess, onUploadError]);

  /**
   * 移除照片
   */
  const handleRemovePhoto = useCallback(() => {
    setUploadedFile(null);
    setPreviewUrl('');
    setQualityAssessment(null);
    
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    
    onPhotoRemove?.();
    message.info('照片已移除');
  }, [previewUrl, onPhotoRemove]);

  /**
   * 获取质量评估颜色
   */
  const getQualityColor = (score: number): string => {
    if (score >= 0.8) return '#52c41a';
    if (score >= 0.6) return '#faad14';
    if (score >= 0.4) return '#fa8c16';
    return '#f5222d';
  };

  /**
   * 获取质量评估文本
   */
  const getQualityText = (score: number): string => {
    if (score >= 0.8) return '优秀';
    if (score >= 0.6) return '良好';
    if (score >= 0.4) return '一般';
    return '较差';
  };

  /**
   * 渲染质量评估
   */
  const renderQualityAssessment = () => {
    if (!qualityAssessment) return null;

    const { score, issues, suggestions, faceDetected, resolution, lighting, blur } = qualityAssessment;

    return (
      <Card size="small" title="质量评估" style={{ marginTop: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* 总体评分 */}
          <Row justify="space-between" align="middle">
            <Col>
              <Text>总体质量</Text>
            </Col>
            <Col>
              <Space>
                <Progress
                  type="circle"
                  size={40}
                  percent={Math.round(score * 100)}
                  strokeColor={getQualityColor(score)}
                  format={() => getQualityText(score)}
                />
              </Space>
            </Col>
          </Row>

          {/* 详细信息 */}
          <Row gutter={16}>
            <Col span={12}>
              <Space direction="vertical" size="small">
                <Text type="secondary">分辨率</Text>
                <Text>{resolution.width} × {resolution.height}</Text>
              </Space>
            </Col>
            <Col span={12}>
              <Space direction="vertical" size="small">
                <Text type="secondary">面部检测</Text>
                <Space>
                  {faceDetected ? (
                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  ) : (
                    <ExclamationCircleOutlined style={{ color: '#f5222d' }} />
                  )}
                  <Text>{faceDetected ? '已检测' : '未检测'}</Text>
                </Space>
              </Space>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Space direction="vertical" size="small">
                <Text type="secondary">光照条件</Text>
                <Text>{lighting === 'good' ? '良好' : lighting === 'acceptable' ? '可接受' : '较差'}</Text>
              </Space>
            </Col>
            <Col span={12}>
              <Space direction="vertical" size="small">
                <Text type="secondary">清晰度</Text>
                <Text>{blur === 'none' ? '清晰' : blur === 'slight' ? '轻微模糊' : '严重模糊'}</Text>
              </Space>
            </Col>
          </Row>

          {/* 问题和建议 */}
          {issues.length > 0 && (
            <Alert
              type="warning"
              message="发现问题"
              description={
                <ul style={{ margin: 0, paddingLeft: 20 }}>
                  {issues.map((issue, index) => (
                    <li key={index}>{issue}</li>
                  ))}
                </ul>
              }
            />
          )}

          {suggestions.length > 0 && (
            <Alert
              type="info"
              message="改进建议"
              description={
                <ul style={{ margin: 0, paddingLeft: 20 }}>
                  {suggestions.map((suggestion, index) => (
                    <li key={index}>{suggestion}</li>
                  ))}
                </ul>
              }
            />
          )}
        </Space>
      </Card>
    );
  };

  return (
    <Card title="照片上传" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 上传区域 */}
        {!uploadedFile ? (
          <Upload
            customRequest={handleUpload}
            showUploadList={false}
            accept="image/*"
            disabled={disabled || isProcessing}
          >
            <div style={{
              border: '2px dashed #d9d9d9',
              borderRadius: 8,
              padding: 40,
              textAlign: 'center',
              cursor: disabled ? 'not-allowed' : 'pointer',
              backgroundColor: disabled ? '#f5f5f5' : '#fafafa'
            }}>
              <CameraOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
              <div>
                <Text>点击或拖拽上传照片</Text>
              </div>
              <div style={{ marginTop: 8 }}>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  支持 JPG、PNG 格式，建议正面清晰照片
                </Text>
              </div>
            </div>
          </Upload>
        ) : (
          /* 预览区域 */
          <div>
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <Text strong>已上传照片</Text>
              </Col>
              <Col>
                <Space>
                  <Tooltip title="预览">
                    <Button
                      type="text"
                      icon={<EyeOutlined />}
                      onClick={() => {
                        // 打开预览模态框
                      }}
                    />
                  </Tooltip>
                  <Tooltip title="删除">
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={handleRemovePhoto}
                    />
                  </Tooltip>
                </Space>
              </Col>
            </Row>

            <Image
              src={previewUrl}
              alt="上传的照片"
              style={{ width: '100%', maxHeight: 200, objectFit: 'cover' }}
              preview={{
                mask: <EyeOutlined />
              }}
            />
          </div>
        )}

        {/* 处理进度 */}
        {isProcessing && (
          <div>
            <Text type="secondary">处理中...</Text>
            <Progress percent={processingProgress} size="small" />
          </div>
        )}

        {/* 质量评估 */}
        {renderQualityAssessment()}

        {/* 使用提示 */}
        <Alert
          type="info"
          message="使用提示"
          description={
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              <li>请使用正面清晰的人脸照片</li>
              <li>确保光线充足，避免阴影遮挡</li>
              <li>建议分辨率不低于400x400像素</li>
              <li>支持JPG、PNG格式，文件大小不超过20MB</li>
            </ul>
          }
          showIcon
        />
      </Space>
    </Card>
  );
};

export default PhotoUploadPanel;
