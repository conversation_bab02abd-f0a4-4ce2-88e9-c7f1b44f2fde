/**
 * AI布局生成器面板组件
 * 提供自动布局生成功能
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Input,
  Select,
  Checkbox,
  Row,
  Col,
  Space,
  Typography,
  Steps,
  List,
  Tag,
  Progress,
  Modal,
  Tabs,
  Rate,
  Image,
  Descriptions,
  Alert,
  Tooltip,
  Divider
} from 'antd';
import {
  RobotOutlined,
  LayoutOutlined,
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  BulbOutlined,
  CodeOutlined,
  EyeOutlined,
  DownloadOutlined,
  ThunderboltOutlined,
  CheckCircleOutlined,
  LoadingOutlined,
  StarOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import AILayoutGeneratorService, {
  LayoutMode,
  Breakpoint,
  LayoutGenerationConfig,
  GeneratedLayout,
  LayoutSuggestion
} from '../../services/AILayoutGeneratorService';
import './AILayoutGeneratorPanel.less';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { Step } = Steps;
const { TabPane } = Tabs;

interface AILayoutGeneratorPanelProps {
  visible: boolean;
  onClose: () => void;
}

const AILayoutGeneratorPanel: React.FC<AILayoutGeneratorPanelProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);
  const [requirements, setRequirements] = useState('');
  const [suggestions, setSuggestions] = useState<LayoutSuggestion[]>([]);
  const [selectedSuggestion, setSelectedSuggestion] = useState<LayoutSuggestion | null>(null);
  const [config, setConfig] = useState<LayoutGenerationConfig>({
    mode: LayoutMode.GRID,
    targetDevices: [Breakpoint.DESKTOP, Breakpoint.TABLET, Breakpoint.MOBILE],
    contentTypes: [],
    colorScheme: 'light',
    designStyle: 'modern',
    accessibility: true,
    performance: true
  });
  const [generatedLayouts, setGeneratedLayouts] = useState<GeneratedLayout[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSuggesting, setIsSuggesting] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [selectedLayout, setSelectedLayout] = useState<GeneratedLayout | null>(null);

  const layoutService = AILayoutGeneratorService.getInstance();

  useEffect(() => {
    if (visible) {
      loadData();
      setupEventListeners();
    }

    return () => {
      cleanupEventListeners();
    };
  }, [visible]);

  const setupEventListeners = () => {
    layoutService.on('suggestionGenerationStarted', handleSuggestionStarted);
    layoutService.on('suggestionGenerationCompleted', handleSuggestionCompleted);
    layoutService.on('layoutGenerationStarted', handleGenerationStarted);
    layoutService.on('layoutGenerationCompleted', handleGenerationCompleted);
  };

  const cleanupEventListeners = () => {
    layoutService.off('suggestionGenerationStarted', handleSuggestionStarted);
    layoutService.off('suggestionGenerationCompleted', handleSuggestionCompleted);
    layoutService.off('layoutGenerationStarted', handleGenerationStarted);
    layoutService.off('layoutGenerationCompleted', handleGenerationCompleted);
  };

  const loadData = () => {
    setSuggestions(layoutService.getLayoutSuggestions());
    setGeneratedLayouts(layoutService.getGeneratedLayouts());
    setIsGenerating(layoutService.isGenerationInProgress());
  };

  const handleSuggestionStarted = () => {
    setIsSuggesting(true);
  };

  const handleSuggestionCompleted = (newSuggestions: LayoutSuggestion[]) => {
    setSuggestions(newSuggestions);
    setIsSuggesting(false);
    setCurrentStep(1);
  };

  const handleGenerationStarted = () => {
    setIsGenerating(true);
  };

  const handleGenerationCompleted = (layout: GeneratedLayout) => {
    setGeneratedLayouts(prev => [...prev, layout]);
    setIsGenerating(false);
    setCurrentStep(2);
  };

  const handleGenerateSuggestions = async () => {
    if (!requirements.trim()) {
      return;
    }

    try {
      await layoutService.generateLayoutSuggestions(requirements);
    } catch (error) {
      console.error('Failed to generate suggestions:', error);
    }
  };

  const handleSelectSuggestion = (suggestion: LayoutSuggestion) => {
    setSelectedSuggestion(suggestion);
    setConfig(prev => ({ ...prev, mode: suggestion.mode }));
  };

  const handleGenerateLayout = async () => {
    try {
      await layoutService.generateLayout(config);
    } catch (error) {
      console.error('Failed to generate layout:', error);
    }
  };

  const handlePreviewLayout = (layout: GeneratedLayout) => {
    setSelectedLayout(layout);
    setPreviewModalVisible(true);
  };

  const handleExportLayout = (layout: GeneratedLayout, format: string) => {
    try {
      const code = layoutService.exportLayout(layout.id, format as any);
      
      // 创建下载链接
      const blob = new Blob([code], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${layout.name.replace(/\s+/g, '_')}.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export layout:', error);
    }
  };

  // 渲染需求输入步骤
  const renderRequirementsStep = () => (
    <Card title={t('ai.layout.describeRequirements')} className="step-card">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Text type="secondary">
          {t('ai.layout.requirementsDescription')}
        </Text>
        <TextArea
          rows={4}
          placeholder={t('ai.layout.requirementsPlaceholder') || '请描述您的布局需求...'}
          value={requirements}
          onChange={(e) => setRequirements(e.target.value)}
        />
        <Button
          type="primary"
          icon={<BulbOutlined />}
          onClick={handleGenerateSuggestions}
          loading={isSuggesting}
          disabled={!requirements.trim()}
        >
          {t('ai.layout.generateSuggestions')}
        </Button>
      </Space>
    </Card>
  );

  // 渲染建议选择步骤
  const renderSuggestionsStep = () => (
    <Card title={t('ai.layout.chooseSuggestion')} className="step-card">
      {suggestions.length > 0 ? (
        <List
          dataSource={suggestions}
          renderItem={(suggestion) => (
            <List.Item
              className={selectedSuggestion?.id === suggestion.id ? 'selected' : ''}
              onClick={() => handleSelectSuggestion(suggestion)}
              style={{ cursor: 'pointer' }}
            >
              <List.Item.Meta
                avatar={<LayoutOutlined style={{ fontSize: '24px', color: '#1890ff' }} />}
                title={
                  <Space>
                    <Text strong>{suggestion.title}</Text>
                    <Rate disabled count={5} value={Math.round(suggestion.confidence / 20)} />
                  </Space>
                }
                description={
                  <div>
                    <Paragraph>{suggestion.description}</Paragraph>
                    <Space wrap>
                      <Text type="secondary">Best for:</Text>
                      {suggestion.bestFor.map(item => (
                        <Tag key={item} color="blue">{item}</Tag>
                      ))}
                    </Space>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      ) : (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <BulbOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
          <Title level={4} type="secondary">
            {t('ai.layout.noSuggestions')}
          </Title>
        </div>
      )}
    </Card>
  );

  // 渲染配置步骤
  const renderConfigStep = () => (
    <Card title={t('ai.layout.configureLayout')} className="step-card">
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>{t('ai.layout.targetDevices')}</Text>
              <Checkbox.Group
                value={config.targetDevices}
                onChange={(devices) => setConfig(prev => ({ ...prev, targetDevices: devices as Breakpoint[] }))}
                style={{ display: 'block', marginTop: 8 }}
              >
                <Row>
                  <Col span={24}>
                    <Checkbox value={Breakpoint.MOBILE}>
                      <MobileOutlined /> {t('ai.layout.mobile')}
                    </Checkbox>
                  </Col>
                  <Col span={24}>
                    <Checkbox value={Breakpoint.TABLET}>
                      <TabletOutlined /> {t('ai.layout.tablet')}
                    </Checkbox>
                  </Col>
                  <Col span={24}>
                    <Checkbox value={Breakpoint.DESKTOP}>
                      <DesktopOutlined /> {t('ai.layout.desktop')}
                    </Checkbox>
                  </Col>
                </Row>
              </Checkbox.Group>
            </div>

            <div>
              <Text strong>{t('ai.layout.designStyle')}</Text>
              <Select
                value={config.designStyle}
                onChange={(style) => setConfig(prev => ({ ...prev, designStyle: style }))}
                style={{ width: '100%', marginTop: 8 }}
              >
                <Option value="modern">{t('ai.layout.modern')}</Option>
                <Option value="classic">{t('ai.layout.classic')}</Option>
                <Option value="minimalist">{t('ai.layout.minimalist')}</Option>
                <Option value="creative">{t('ai.layout.creative')}</Option>
              </Select>
            </div>
          </Space>
        </Col>
        <Col span={12}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>{t('ai.layout.colorScheme')}</Text>
              <Select
                value={config.colorScheme}
                onChange={(scheme) => setConfig(prev => ({ ...prev, colorScheme: scheme }))}
                style={{ width: '100%', marginTop: 8 }}
              >
                <Option value="light">{t('ai.layout.light')}</Option>
                <Option value="dark">{t('ai.layout.dark')}</Option>
                <Option value="auto">{t('ai.layout.auto')}</Option>
              </Select>
            </div>

            <div>
              <Text strong>{t('ai.layout.options')}</Text>
              <div style={{ marginTop: 8 }}>
                <Checkbox
                  checked={config.accessibility}
                  onChange={(e) => setConfig(prev => ({ ...prev, accessibility: e.target.checked }))}
                >
                  {t('ai.layout.accessibility')}
                </Checkbox>
                <br />
                <Checkbox
                  checked={config.performance}
                  onChange={(e) => setConfig(prev => ({ ...prev, performance: e.target.checked }))}
                >
                  {t('ai.layout.performance')}
                </Checkbox>
              </div>
            </div>
          </Space>
        </Col>
      </Row>

      <Divider />

      <Button
        type="primary"
        icon={<ThunderboltOutlined />}
        onClick={handleGenerateLayout}
        loading={isGenerating}
        disabled={!selectedSuggestion}
        size="large"
      >
        {t('ai.layout.generateLayout')}
      </Button>
    </Card>
  );

  // 渲染结果步骤
  const renderResultsStep = () => (
    <Card title={t('ai.layout.generatedLayouts')} className="step-card">
      {generatedLayouts.length > 0 ? (
        <List
          grid={{ gutter: 16, xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 2 }}
          dataSource={generatedLayouts}
          renderItem={(layout) => (
            <List.Item>
              <Card
                hoverable
                cover={
                  <div style={{ height: 200, backgroundColor: '#f0f2f5', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <LayoutOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                  </div>
                }
                actions={[
                  <Tooltip title={t('ai.layout.preview')}>
                    <EyeOutlined onClick={() => handlePreviewLayout(layout)} />
                  </Tooltip>,
                  <Tooltip title={t('ai.layout.exportHTML')}>
                    <DownloadOutlined onClick={() => handleExportLayout(layout, 'html')} />
                  </Tooltip>,
                  <Tooltip title={t('ai.layout.exportCSS')}>
                    <CodeOutlined onClick={() => handleExportLayout(layout, 'css')} />
                  </Tooltip>
                ]}
              >
                <Card.Meta
                  title={layout.name}
                  description={
                    <div>
                      <Text type="secondary">{layout.description}</Text>
                      <br />
                      <Space style={{ marginTop: 8 }}>
                        <Tag color="blue">Complexity: {layout.metadata.complexity}%</Tag>
                        <Tag color="green">Performance: {layout.metadata.performance}%</Tag>
                      </Space>
                    </div>
                  }
                />
              </Card>
            </List.Item>
          )}
        />
      ) : (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <LayoutOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
          <Title level={4} type="secondary">
            {t('ai.layout.noLayouts')}
          </Title>
        </div>
      )}
    </Card>
  );

  const steps = [
    {
      title: t('ai.layout.requirements'),
      content: renderRequirementsStep()
    },
    {
      title: t('ai.layout.suggestions'),
      content: renderSuggestionsStep()
    },
    {
      title: t('ai.layout.configuration'),
      content: renderConfigStep()
    },
    {
      title: t('ai.layout.results'),
      content: renderResultsStep()
    }
  ];

  return (
    <Modal
      title={
        <Space>
          <RobotOutlined />
          {t('ai.layout.aiLayoutGenerator')}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={null}
      className="ai-layout-generator-panel"
    >
      <div className="layout-generator-content">
        <Steps current={currentStep} onChange={setCurrentStep} style={{ marginBottom: 24 }}>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <div className="step-content">
          {steps[currentStep].content}
        </div>

        {isGenerating && (
          <Alert
            message={t('ai.layout.generating')}
            description={t('ai.layout.generatingDescription')}
            type="info"
            showIcon
            icon={<LoadingOutlined />}
            style={{ marginTop: 16 }}
          />
        )}
      </div>

      {/* 预览对话框 */}
      <Modal
        title={t('ai.layout.layoutPreview')}
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>
            {t('common.close')}
          </Button>
        ]}
      >
        {selectedLayout && (
          <Tabs defaultActiveKey="preview">
            <TabPane tab={t('ai.layout.preview')} key="preview">
              <div style={{ height: 400, backgroundColor: '#f0f2f5', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Text type="secondary">{t('ai.layout.previewPlaceholder')}</Text>
              </div>
            </TabPane>
            <TabPane tab="HTML" key="html">
              <pre style={{ maxHeight: 400, overflow: 'auto', backgroundColor: '#f6f8fa', padding: 16 }}>
                {selectedLayout.html}
              </pre>
            </TabPane>
            <TabPane tab="CSS" key="css">
              <pre style={{ maxHeight: 400, overflow: 'auto', backgroundColor: '#f6f8fa', padding: 16 }}>
                {selectedLayout.css}
              </pre>
            </TabPane>
          </Tabs>
        )}
      </Modal>
    </Modal>
  );
};

export default AILayoutGeneratorPanel;
